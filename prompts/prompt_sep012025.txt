```
Given the project analysis data:
- Project Name: {{projectName}}
- Project Type: {{projectType}}
- Score: {{score}}/40
- MVP Readiness Level: {{readinessLevel}}
- Strengths: {{strengths}}
- Weaknesses: {{weaknesses}}
Generate an executive summary for a Vibe Coder. Include:
- The project name, type, and score.
- The MVP readiness level with a brief explanation.
- The top 3 strengths and top 3 areas for improvement, formatted as bullet points.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent (e.g., GitHub Copilot) to address the top areas for improvement overall, focusing on rapid iteration.
Keep the tone concise, professional, and encouraging for rapid iteration.
```

**Updated Example Output:**

```
**Executive Summary**
**Project**: v0 App, Web Application, 24/40
**MVP Readiness**: Early MVP - Your project has basic viability but needs more development before release.
**Top 3 Strengths**:
- Modern tech stack, including Next.js and Tailwind CSS.
- Consistent use of components for UI across different pages.
- Minimal feature set, focusing on the primary goal.
**Top 3 Areas for Improvement**:
- Add visible mechanisms for gathering user feedback.
- Improve documentation for users and developers.
- Increase testing to ensure technical stability.

**Coding Agent Prompt**: "Based on my project's MVP report, improve the top areas: add a user feedback form in React with Formik and backend endpoint in Express to collect input; create a comprehensive README.md with sections for installation, usage, and contributing; implement unit tests with Jest for key components like auth.js and index.js. Use my existing tech stack (React, Next.js, Tailwind CSS) and focus on rapid iteration for quick deployment."
```

#### 4.2 Detailed Scoring Breakdown with Code-Level Insights

**Updated Prompt:**

```
Given the project analysis data for criterion {{criterion}}:
- Score: {{score}}/5
- Explanation: {{explanation}}
- Files Analyzed: {{files}}
- Code Issues: {{issues}}
- Technologies: {{technologies}}
Generate a detailed breakdown for a Vibe Coder. Include:
- The criterion name, score, and explanation.
- Specific examples from the project (e.g., file names, code snippets).
- A code snippet for improvement if applicable.
- Actionable suggestions for improvement.
At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement the suggestions for this criterion, referencing specific files and issues.
Keep the tone technical, actionable, and focused on rapid iteration.
```

**Updated Example Output:**

```
**Feedback Collection: 2/5**
**Explanation**: There is no clear mechanism for collecting user feedback, which is crucial for iterating based on user needs.
**Example**: In index.js, there’s no endpoint for user surveys or feedback forms. The project lacks any visible feedback integration.
**Code Snippet for Improvement**:
```javascript
// Add to index.js
app.post('/feedback', (req, res) => {
  const feedback = req.body;
  // Save feedback to database (e.g., Firebase)
  res.status(200).send('Feedback received');
});
```

**Suggestions**:

- Implement a simple feedback form using Formik in React to collect user input.
- Integrate a tool like Google Analytics to track user behavior passively.

**Coding Agent Prompt**: "For my project's Feedback Collection criterion (score 2/5), add a feedback form in React using Formik in a new FeedbackForm.js component, and create a POST endpoint in index.js to handle submissions and save to Firebase. Reference the existing index.js for API structure, and ensure it's integrated into the main app for quick user testing."

```

#### 4.3 Technology Stack Analysis
**Updated Prompt:**
```

Given the project analysis data:

- Technologies: {{technologies}}
- Performance Metrics: {{performance}}
- Dependencies: {{dependencies}}
  Generate a technology stack analysis for a Vibe Coder. Include:
- A list of detected technologies.
- Pros and cons of the stack for rapid development.
- Recommendations for optimization.
- Alternative tools if applicable.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to apply the recommendations, such as optimizing dependencies or switching tools.
  Keep the tone technical and focused on scalability and speed.

```

**Updated Example Output:**
```

**Technology Stack Analysis**
**Detected Technologies**: Next.js, Tailwind CSS, React, TypeScript, Radix UI
**Pros**:

- Next.js enables fast iteration with server-side rendering.
- Tailwind CSS speeds up UI development with utility classes.
- TypeScript improves code reliability.
  **Cons**:
- Large Next.js bundles may slow performance (current load time: 3.2s).
- Tailwind CSS can lead to bloated CSS files if not purged properly.
  **Recommendations**:
- Lazy-load components in Next.js to improve load times.
- Use next-purge-css to remove unused Tailwind styles.
  **Alternatives**:
- For smaller projects, consider PicoCSS for a lighter CSS framework.

**Coding Agent Prompt**: "Optimize my project's tech stack: implement lazy-loading for components in Next.js (focus on index.js and main app files), add next-purge-css to package.json and configure it to remove unused Tailwind styles, and check performance metrics like load time. Use my current dependencies (Next.js, Tailwind CSS, React) for scalability improvements."

```

#### 4.4 User Feedback Strategy Guide
**Updated Prompt:**
```

Given the project analysis data for Feedback Collection:

- Score: {{score}}/5
- Gaps: {{gaps}}
- Technologies: {{technologies}}
  Generate a user feedback strategy guide for a Vibe Coder. Include:
- The current score and identified gaps.
- A step-by-step guide to add a feedback system.
- A code snippet for implementation.
- Best practices for user feedback.
- Recommended tools.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement the step-by-step guide, including the code snippet.
  Keep the tone practical and encouraging for rapid implementation.

```

**Updated Example Output:**
```

**User Feedback Strategy Guide**
**Current Score**: 2/5
**Gaps**: No visible feedback mechanism detected in the project.
**Step-by-Step Guide**:

1. Create a feedback form component in React using React Hook Form.
2. Add a POST endpoint in index.js to handle form submissions.
3. Store responses in Firebase for analysis.
4. Display the form on a dedicated /feedback page.
   **Code Snippet**:

```javascript
// FeedbackForm.js
import { useForm } from 'react-hook-form';
export default function FeedbackForm() {
  const { register, handleSubmit } = useForm();
  const onSubmit = async (data) => {
    await fetch('/feedback', { method: 'POST', body: JSON.stringify(data) });
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input {...register('message')} placeholder="Your feedback" />
      <button type="submit">Submit</button>
    </form>
  );
}
```

**Best Practices**:

- Keep surveys short (<5 questions) to maximize responses.
- Offer incentives (e.g., early access) for feedback.
  **Tools**:
- Hotjar for heatmaps.
- Google Analytics for user behavior tracking.

**Coding Agent Prompt**: "Implement a user feedback system in my React project: create a FeedbackForm.js component using React Hook Form for input, add a POST /feedback endpoint in index.js to handle submissions and store in Firebase, integrate the form into a new /feedback page. Use my existing technologies (React, Next.js) and ensure it's ready for quick deployment."

```

#### 4.5 Deployment and Launch Checklist
**Updated Prompt:**
```

Given the project analysis data for Launch Readiness:

- Score: {{score}}/5
- Gaps: {{gaps}}
- Technologies: {{technologies}}
  Generate a deployment and launch checklist for a Vibe Coder. Include:
- The current score and identified gaps.
- A checklist for launch preparation.
- A suggested timeline.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to set up deployment based on the checklist, referencing technologies.
  Keep the tone actionable and focused on rapid deployment.

```

**Updated Example Output:**
```

**Deployment and Launch Checklist**
**Current Score**: 3/5
**Gaps**: Ready for closed beta but not public release due to untested features.
**Checklist**:

- **Hosting**: Deploy on Vercel for Next.js projects.
- **Performance**: Minify CSS/JS, enable compression.
- **Security**: Add helmet.js for HTTP headers.
- **Monitoring**: Integrate Sentry for error tracking.
  **Timeline**:
- Week 1: Fix bugs and deploy to Vercel.
- Week 2: Beta test with 10 users and gather feedback.

**Coding Agent Prompt**: "Set up deployment for my Next.js project: configure Vercel hosting with steps for deployment, add minification and compression for CSS/JS in build settings, install and integrate helmet.js for security in index.js, and add Sentry for monitoring. Focus on rapid launch with my current tech stack."

```

#### 4.6 Testing and Stability Recommendations
**Updated Prompt:**
```

Given the project analysis data for Testing:

- Score: {{score}}/5
- Gaps: {{gaps}}
- Files: {{files}}
  Generate testing and stability recommendations for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific testing gaps (e.g., missing tests for a file).
- Recommended frameworks.
- Sample test cases.
- A user testing plan.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to implement tests based on the recommendations and sample cases.
  Keep the tone technical and actionable.

```

**Updated Example Output:**
```

**Testing and Stability Recommendations**
**Current Score**: 2/5
**Gaps**: No clear evidence of comprehensive testing strategy or user testing.
**Testing Gaps**:

- No unit tests for auth.js.
- No end-to-end tests detected.
  **Recommended Frameworks**:
- Jest for unit tests.
- Cypress for E2E tests.
  **Sample Test Case**:

```javascript
// auth.test.js
test('user login', () => {
  const user = { email: '<EMAIL>', password: 'password' };
  expect(login(user)).toBe(true);
});
```

**User Testing Plan**:

- Recruit 5 beta testers via Discord.
- Ask them to test the login flow and provide feedback.

**Coding Agent Prompt**: "Add testing to my project: implement Jest unit tests for auth.js including the sample login test case, set up Cypress for E2E tests on key flows like login, and create a CI script for GitHub Actions. Address gaps in my files (e.g., auth.js) for improved stability."

```

#### 4.7 Documentation Enhancement Plan
**Updated Prompt:**
```

Given the project analysis data for Documentation:

- Score: {{score}}/5
- Gaps: {{gaps}}
- Files: {{files}}
  Generate a documentation enhancement plan for a Vibe Coder. Include:
- The current score and identified gaps.
- Specific documentation gaps.
- A README template.
- Recommended tools.
- An example action.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to generate or update documentation based on the plan.
  Keep the tone practical and focused on quick wins.

```

**Updated Example Output:**
```

**Documentation Enhancement Plan**
**Current Score**: 2/5
**Gaps**: Limited mention of documentation; no user guide.
**Documentation Gaps**:

- No user guide in the project.
- README lacks setup instructions.
  **README Template**:
- Installation: Steps to install dependencies.
- Usage: How to run the project.
- Contributing: Guidelines for contributors.
  **Tools**:
- Use Docusaurus for a user-friendly docs site.
  **Example**:
- Add a docs/getting-started.md file with setup steps.

**Coding Agent Prompt**: "Generate enhanced documentation for my project: create a README.md using the provided template with sections for installation, usage, and contributing; add a getting-started.md in a docs folder with setup steps. Use Docusaurus if possible, and reference my files for accurate content."

```

#### 4.8 Competitive Benchmarking
**Updated Prompt:**
```

Given the project analysis data:

- Score: {{score}}/40
- Criteria Scores: {{criteriaScores}}
- Industry Average: {{industryAverage}}
  Generate a competitive benchmarking section for a Vibe Coder. Include:
- A comparison of the project’s score to the industry average.
- Areas where the project excels.
- Areas to improve to match competitors.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to address areas to improve and align with industry averages.
  Keep the tone motivational and data-driven.

```

**Updated Example Output:**
```

**Competitive Benchmarking**
**Comparison**: Your project scores 24/40, compared to an industry average of 28/40 for similar Next.js MVPs.
**Areas of Excellence**:

- UI consistency is better than 70% of peers.
  **Areas to Improve**:
- Top MVPs have 80% test coverage; aim for 60% to start.

**Coding Agent Prompt**: "To match industry benchmarks in my project (score 24/40 vs. average 28/40), improve testing to 60% coverage using Jest on key files like index.js, and enhance UI consistency with Tailwind best practices. Focus on areas like test coverage and performance."

```

#### 4.9 Resource Library
**Updated Prompt:**
```

Given the project analysis data:

- Gaps: {{gaps}}
- Technologies: {{technologies}}
  Generate a resource library for a Vibe Coder. Include:
- Tutorials relevant to identified gaps.
- Tools to address gaps.
- Communities for support.
- Templates for quick implementation.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to integrate resources or templates into their project.
  Keep the tone helpful and resourceful.

```

**Updated Example Output:**
```

**Resource Library**
**Tutorials**:

- "How to Add Feedback Forms in React" (link).
  **Tools**:
- Free testing tools: Jest, Cypress.
  **Communities**:
- Join the Vibe Coding Discord for peer feedback.
  **Templates**:
- Download a sample README template (link).

**Coding Agent Prompt**: "Integrate resources into my project: use a sample README template to update my README.md, add Jest for testing based on tutorials, and include a feedback form from a React tutorial. Reference my gaps like missing tests and documentation."

```

#### 4.10 Personalized Roadmap
**Updated Prompt:**
```

Given the project analysis data:

- Score: {{score}}/40
- Weaknesses: {{weaknesses}}
- Recommendations: {{recommendations}}
  Generate a personalized roadmap for a Vibe Coder. Include:
- The current and target MVP readiness level.
- Actionable steps with priorities and estimated times.
- A suggested timeline.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to automate or assist with the roadmap steps.
  Keep the tone actionable and encouraging.

```

**Updated Example Output:**
```

**Personalized Roadmap**
**Current Level**: Early MVP (24/40)
**Target Level**: Solid MVP (26-35)
**Actionable Steps**:

- **High Priority**: Add feedback form (est. 2 hours).
- **Medium Priority**: Write user guide (est. 4 hours).
- **Low Priority**: Optimize images (est. 3 hours).
  **Timeline**: Reach Solid MVP in 2 weeks with 10 hours of work.

**Coding Agent Prompt**: "Help implement my personalized roadmap: start with high-priority steps like adding a feedback form in React (2 hours), then generate a user guide Markdown file (4 hours), and optimize images in my assets folder (3 hours). Use my tech stack and aim for Solid MVP level."

```

#### 4.11 Visualizations and Metrics
**Updated Prompt:**
```

Given the project analysis data:

- Scores: {{scores}}
- Code Metrics: {{codeMetrics}}
- Performance Metrics: {{performanceMetrics}}
- Industry Average: {{industryAverage}}
  Generate visualizations and metrics for a Vibe Coder. Include:
- A radar chart of scores.
- Code quality metrics (e.g., coverage, complexity).
- Performance metrics (e.g., load time).
- A comparison chart vs. industry average.
  At the end, add a **Coding Agent Prompt** subsection with a single, effective prompt the user can copy and use with a coding agent to improve metrics like code coverage or performance.
  Keep the tone data-driven and visual.

```

**Updated Example Output:**
```

**Visualizations and Metrics**
**Radar Chart**: [Embed SVG of radar chart]
**Code Quality Metrics**:

- Code Coverage: 40%
- Cyclomatic Complexity: 5
  **Performance Metrics**:
- Page Load Time: 3.2s (Target: <2s)
  **Comparison Chart**: [Embed bar chart: Your score (24) vs. Industry Average (28)]

**Coding Agent Prompt**: "Improve my project's metrics: increase code coverage to 60% by adding Jest tests to uncovered files, reduce cyclomatic complexity in complex functions like in index.js, and optimize page load time below 2s with lazy-loading in React. Reference my current metrics."

```

#### 4.12 Developer Journey Map (New Section)
**New Prompt:**
```

Given the project analysis data:

- Project Name: {{projectName}}
- Score: {{score}}/40
- Readiness Level: {{readinessLevel}}
- Weaknesses: {{weaknesses}}
- Recommendations: {{recommendations}}
- Personalized Roadmap: {{roadmap}}  # From section 4.10
  Generate a developer journey map to help finish the project for a Vibe Coder. Include:
- A step-by-step journey map in table format (e.g., Step, Description, Priority, Estimated Time, Related Sections).
- Key milestones tied to the roadmap (e.g., from Early MVP to Solid MVP).
- Tips for iteration and launch.
  Keep the tone encouraging and structured for easy following.

```

**Example Output:**
```

**Developer Journey Map**

This journey map outlines the steps to complete your project and reach MVP launch, based on your score (24/40) and recommendations. Follow this path for rapid iteration.

| **Step** | **Description** | **Priority** | **Estimated Time** | **Related Sections** |
|----------|-----------------|--------------|---------------------|----------------------|
| 1. Address Core Gaps | Fix high-priority weaknesses like adding feedback mechanisms and basic tests. | High | 4-6 hours | Feedback Collection, Testing |
| 2. Optimize Tech Stack | Implement optimizations (e.g., lazy-loading) and check performance metrics. | Medium | 3-5 hours | Technology Stack Analysis |
| 3. Enhance Documentation & UI | Update README and add user-centric design improvements. | Medium | 4-6 hours | Documentation Plan, User-Centric Design |
| 4. Test & Deploy | Run tests, set up deployment, and beta test. | High | 6-8 hours | Testing Recommendations, Deployment Checklist |
| 5. Launch & Iterate | Go live, collect feedback, and monitor. | High | Ongoing (2 weeks) | Launch Readiness, Personalized Roadmap |

**Key Milestones**:

- Week 1: Reach 26/40 (Solid MVP start) by completing steps 1-2.
- Week 2: Full Solid MVP (30+/40) with testing and deployment.

**Tips**: Iterate weekly based on feedback, use your coding agent for quick implementations, and track progress against metrics like code coverage.